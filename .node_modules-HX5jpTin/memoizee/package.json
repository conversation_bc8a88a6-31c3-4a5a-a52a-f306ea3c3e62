{"name": "memoizee", "version": "0.4.15", "description": "Memoize/cache function results", "author": "<PERSON><PERSON> <<EMAIL>> (http://www.medikoo.com/)", "keywords": ["memoize", "memoizer", "cache", "memoization", "memo", "memcached", "hashing.", "storage", "caching", "memory", "gc", "weak", "garbage", "collector", "async"], "repository": {"type": "git", "url": "git://github.com/medikoo/memoizee.git"}, "dependencies": {"d": "^1.0.1", "es5-ext": "^0.10.53", "es6-weak-map": "^2.0.3", "event-emitter": "^0.3.5", "is-promise": "^2.2.2", "lru-queue": "^0.1.0", "next-tick": "^1.1.0", "timers-ext": "^0.1.7"}, "devDependencies": {"bluebird": "^3.7.2", "eslint": "^5.16.0", "eslint-config-medikoo-es5": "^1.7.3", "plain-promise": "^0.1.1", "tad": "^2.0.1"}, "eslintConfig": {"extends": "medikoo-es5", "root": true, "globals": {"setTimeout": true, "clearTimeout": true}, "rules": {"max-lines-per-function": "off"}}, "scripts": {"lint": "eslint --ignore-path=.gitignore .", "test": "tad"}, "license": "ISC"}
{"name": "postcss-ordered-values", "version": "6.0.2", "description": "Ensure values are ordered consistently in your CSS.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["src", "LICENSE-MIT", "types"], "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"postcss-value-parser": "^4.2.0", "cssnano-utils": "^4.0.2"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "devDependencies": {"postcss": "^8.4.35"}, "peerDependencies": {"postcss": "^8.4.31"}}
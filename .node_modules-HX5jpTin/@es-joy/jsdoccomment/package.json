{"name": "@es-joy/jsdoccomment", "version": "0.48.0", "author": "<PERSON> <<EMAIL>>", "contributors": [], "description": "Maintained replacement for ESLint's deprecated SourceCode#getJSDocComment along with other jsdoc utilities", "license": "MIT", "keywords": ["ast", "comment", "estree", "jsdoc", "parser", "eslint", "sourcecode"], "type": "module", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./src/index.js", "require": "./dist/index.cjs.cjs"}, "browserslist": ["defaults, not op_mini all"], "typedocOptions": {"dmtLinksService": {"GitHub": "https://github.com/es-joy/jsdoccomment", "NPM": "https://www.npmjs.com/package/@es-joy/jsdoccomment"}}, "repository": {"type": "git", "url": "git+https://github.com/es-joy/jsdoccomment.git"}, "bugs": {"url": "https://github.com/es-joy/jsdoccomment/issues"}, "homepage": "https://github.com/es-joy/jsdoccomment", "engines": {"node": ">=16"}, "dependencies": {"comment-parser": "1.4.1", "esquery": "^1.6.0", "jsdoc-type-pratt-parser": "~4.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/preset-env": "^7.25.3", "@rollup/plugin-babel": "^6.0.4", "@types/eslint": "^9.6.0", "@types/esquery": "^1.5.4", "@types/estraverse": "^5.1.7", "@types/estree": "^1.0.5", "@typescript-eslint/types": "^8.1.0", "@typescript-eslint/visitor-keys": "^8.1.0", "@typhonjs-build-test/esm-d-ts": "0.3.0-next.1", "@typhonjs-typedoc/typedoc-pkg": "^0.0.5", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "eslint": "^9.9.0", "eslint-config-ash-nazg": "36.12.0", "espree": "^10.1.0", "estraverse": "^5.3.0", "rollup": "^4.20.0", "typescript": "^5.5.4", "typescript-eslint": "^8.1.0", "vitest": "^2.0.5"}, "files": ["/dist", "/src", "CHANGES.md", "LICENSE-MIT.txt"], "scripts": {"build": "rollup -c && npm run types", "docs": "typedoc-pkg --api-link es", "eslint": "eslint .", "lint": "npm run eslint --", "open": "open ./coverage/index.html", "test": "npm run lint && npm run build && npm run test-ui", "test-ui": "vitest --ui --coverage", "test-cov": "vitest --coverage", "tsc": "tsc", "types": "esm-d-ts gen ./src/index.js --output ./dist/index.d.ts"}}
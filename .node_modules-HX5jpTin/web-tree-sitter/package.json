{"name": "web-tree-sitter", "version": "0.20.8", "description": "Tree-sitter bindings for the web", "main": "tree-sitter.js", "types": "tree-sitter-web.d.ts", "directories": {"test": "test"}, "scripts": {"test": "mocha", "prepack": "cp ../../LICENSE .", "prepublishOnly": "node check-artifacts-fresh.js"}, "repository": {"type": "git", "url": "git+https://github.com/tree-sitter/tree-sitter.git"}, "keywords": ["incremental", "parsing"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/tree-sitter/tree-sitter/issues"}, "homepage": "https://github.com/tree-sitter/tree-sitter/tree/master/lib/binding_web", "devDependencies": {"chai": "^4.3.7", "mocha": "^10.2.0", "terser": "^5.16.6"}}